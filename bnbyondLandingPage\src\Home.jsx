import React from 'react'

function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* SECTION 1: <PERSON> Banner */}
      <div className="relative h-screen bg-cover bg-center" style={{backgroundImage: "url('https://images.unsplash.com/photo-1512917774080-9991f1c4c750?auto=format&fit=crop&w=1500&q=80')"}}>
        <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        <div className="relative z-10 flex flex-col items-center justify-center h-full text-white px-4">
          <h1 className="text-5xl md:text-6xl font-bold text-center mb-8 max-w-4xl leading-tight">
            Your Free Membership Awaits.
          </h1>
          <div className="bg-pink-500 px-8 py-4 rounded-full">
            <span className="text-white font-bold text-xl">
              The STR Platform Where Hosts Host Hosts!
            </span>
          </div>
        </div>
      </div>

      {/* SECTION 2: Offer Intro */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
            For Members of "Airbnb Hosts - Optimize My Airbnb Listing"<br />
            Your 100% Free Lifetime BnByond Membership Awaits!
          </h2>

          <div className="max-w-4xl mx-auto text-left space-y-4">
            <p className="text-lg text-gray-700">Hello fellow Airbnb Hosts!</p>

            <p className="text-gray-700">Want to transform your unbooked rental nights into free travel experiences?</p>

            <p className="text-gray-700">
              BnByond is thrilled to announce that members of Airbnb Hosts - Optimize My Airbnb Listing are being provided
              with an exclusive opportunity: a 100% FREE lifetime membership to our revolutionary vacation rental property
              owners exchange community! This membership normally costs $88 annually, but for members of your group, it's
              entirely on us, forever. You can check out the platform at beta.bnbyond.com
            </p>

            <p className="text-gray-700">
              What is BnByond? After several years of development, we are now rolling out the ultimate booking platform where
              hosts host hosts. It's pretty simple. You host other STR owners. You get points. You use those points to travel anywhere.
              Unlike traditional booking platforms or simple home-swap services, BnByond is an exclusive exchange community
              designed by and for vacation rental property owners.
            </p>
          </div>
        </div>

        {/* SECTION 3: Feature Grid (4 Cards) */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <div className="space-y-8">
            {/* Card 1 - Green */}
            <div className="bg-green-100 p-6 rounded-lg">
              <h3 className="text-xl font-bold text-green-800 mb-3 uppercase">TRAVEL FOR FREE!</h3>
              <p className="text-green-700">
                Your earned points act as currency, allowing you to stay at other member properties around the world without the expense of accommodations.
              </p>
            </div>

            {/* Card 2 - Red */}
            <div className="bg-red-100 p-6 rounded-lg">
              <h3 className="text-xl font-bold text-red-800 mb-3 uppercase">LIFETIME CHARTER MEMBERSHIP</h3>
              <p className="text-red-700">
                We are hoping that you – as a Charter Member – will help steer the platform and help decide on new features for years to come. This isn't mandatory.
              </p>
            </div>

            {/* Card 3 - Yellow */}
            <div className="bg-yellow-100 p-6 rounded-lg">
              <h3 className="text-xl font-bold text-yellow-800 mb-3 uppercase">JOIN A TRUSTED COMMUNITY</h3>
              <p className="text-yellow-700">
                Host fellow STR owners who understand the effort and care required for STRs, leading to more respectful guests and a more positive experience, tips and connections.
              </p>
            </div>

            {/* Card 4 - Blue */}
            <div className="bg-blue-100 p-6 rounded-lg">
              <h3 className="text-xl font-bold text-blue-800 mb-3 uppercase">CONVERT UNBOOKED TIME INTO VALUE</h3>
              <p className="text-blue-700">
                BnByond empowers you to leverage this downtime, especially during shoulder and off-peak seasons, by hosting other members and earning points.
              </p>
            </div>
          </div>

          {/* SECTION 4: Property Preview Cards */}
          <div className="space-y-4">
            <div className="bg-white border rounded-lg overflow-hidden shadow-sm">
              <img src="https://via.placeholder.com/300x150" alt="Property 1" className="w-full h-32 object-cover" />
              <div className="p-4">
                <h4 className="font-semibold text-gray-800">Cozy Mountain Retreat</h4>
                <p className="text-sm text-gray-600">123 Alpine Drive, Aspen, CO</p>
                <p className="text-sm font-medium text-blue-600 mt-2">100 Points</p>
              </div>
            </div>

            <div className="bg-white border rounded-lg overflow-hidden shadow-sm">
              <img src="https://via.placeholder.com/300x150" alt="Property 2" className="w-full h-32 object-cover" />
              <div className="p-4">
                <h4 className="font-semibold text-gray-800">Beachfront Villa</h4>
                <p className="text-sm text-gray-600">456 Ocean View Blvd, Miami, FL</p>
                <p className="text-sm font-medium text-blue-600 mt-2">100 Points</p>
              </div>
            </div>

            <div className="bg-white border rounded-lg overflow-hidden shadow-sm">
              <img src="https://via.placeholder.com/300x150" alt="Property 3" className="w-full h-32 object-cover" />
              <div className="p-4">
                <h4 className="font-semibold text-gray-800">Urban Loft</h4>
                <p className="text-sm text-gray-600">789 Downtown St, New York, NY</p>
                <p className="text-sm font-medium text-blue-600 mt-2">100 Points</p>
              </div>
            </div>
          </div>
        </div>

        {/* SECTION 5: Exclusive Offer */}
        <div className="bg-gray-50 p-8 rounded-lg mb-16">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            Your Exclusive Offer Details: As a Charter Member from the "Airbnb Hosts - Optimize My Airbnb Listing" group, you receive:
          </h3>
          <ul className="space-y-3 max-w-4xl mx-auto">
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">
                <strong>100% Free Lifetime Membership</strong> (normally $88/year)
              </span>
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">
                <strong>3,000 Starting Points:</strong> You'll receive 1,000 standard credit points PLUS an additional 2,000 bonus points to use for bookings right away. This gives you an incredible head start on your travel adventures!
              </span>
            </li>
          </ul>
        </div>

        {/* SECTION 6: Simple & Transparent Fees */}
        <div className="text-center mb-16">
          <h3 className="text-3xl font-bold text-gray-800 mb-8">Simple & Transparent Fees</h3>
          <ul className="space-y-4 max-w-3xl mx-auto text-left">
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">You will pay no commission fees as a Host.</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">When you act as a Guest, there's only a modest 5% fee on the point value of your stay.</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">You will pay nothing for membership.</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">
                <strong>List Multiple Properties:</strong> As a member of this exclusive Facebook group, you can list up to five properties on BnByond under your single, free lifetime membership.
              </span>
            </li>
          </ul>
        </div>

        {/* SECTION 7: What Happens Next */}
        <div className="text-center mb-16">
          <h3 className="text-3xl font-bold text-gray-800 mb-6">What Happens Next?</h3>
          <p className="text-gray-700 leading-relaxed max-w-4xl mx-auto">
            Fill in the form below. We'll send clear instructions on how to easily fill out your profile, upload your property(s) details, and fill out your availability and pricing on your BnByond calendar – a process designed to feel familiar, similar to other platforms you already use. You'll then be among the first to know when you can start booking. This is an incredible opportunity to maximize your STR investment and see the world thanks to your Facebook community Airbnb Hosts - Optimize My Airbnb Listing
          </p>
        </div>

        {/* SECTION 8: Sign-Up Form */}
        <div className="bg-gray-50 p-8 rounded-lg mb-16">
          <div className="flex flex-col lg:flex-row items-start justify-between gap-8">
            <div className="lg:w-2/3">
              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="First Name"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="text"
                    placeholder="Last Name"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <input
                  type="email"
                  placeholder="Email"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />

                <div className="mt-6">
                  <p className="text-sm text-gray-700 mb-4 font-medium">State or States where your STR is located</p>
                  <div className="space-y-2">
                    {[1, 2, 3, 4, 5].map((num) => (
                      <div key={num} className="flex items-center">
                        <span className="text-gray-600 mr-3 w-4">{num}.</span>
                        <input
                          type="text"
                          className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mt-6">
                  <button
                    type="submit"
                    className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
                  >
                    Contact Us
                  </button>
                </div>
              </form>
            </div>

            <div className="lg:w-1/3 flex justify-center">
              <div className="text-center">
                <img src="https://via.placeholder.com/150x100?text=BnByond+Logo" alt="BnByond Logo" className="mx-auto mb-4" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* SECTION 9: Footer Disclaimer */}
      <div className="bg-gray-100 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <p className="text-xs text-gray-500 text-center leading-relaxed">
            BnByond is independently host-to-host vacation exchange network and is not affiliated with Airbnb, VRBO, or any other short-term rental platforms. Membership benefits, including point-based stays, are subject to availability and may change without notice. Properties featured on the site are listed by independent hosts/owners. By joining, you agree to our Terms of Service and Community Guidelines.
          </p>
        </div>
      </div>
    </div>
  )
}

export default Home
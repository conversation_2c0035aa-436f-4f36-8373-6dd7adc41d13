import React from 'react'

function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="relative h-screen bg-cover bg-center" style={{backgroundImage: "url('https://images.unsplash.com/photo-1551632811-561732d1e306?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')"}}>
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="relative z-10 flex flex-col items-center justify-center h-full text-white px-4">
          {/* Logo */}
          <div className="mb-8">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-orange-400 via-red-400 to-pink-400 rounded-full"></div>
              <div className="w-6 h-6 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full"></div>
              <div className="w-4 h-4 bg-gradient-to-br from-green-400 to-emerald-400 rounded-full"></div>
            </div>
            <h1 className="text-4xl font-bold mt-2">bnbyond</h1>
          </div>

          {/* Main Heading */}
          <h2 className="text-5xl md:text-6xl font-bold text-center mb-8 max-w-4xl leading-tight">
            Your Free Membership Awaits.
          </h2>

          {/* CTA Button */}
          <div className="bg-red-500 hover:bg-red-600 transition-colors duration-200 px-8 py-4 rounded-lg">
            <span className="text-white font-semibold text-xl">
              The STR Platform Where Hosts Host Hosts!
            </span>
          </div>
        </div>
      </div>

      {/* Main Content Section */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-gray-800 mb-4">
            For Members of "Airbnb Hosts - Optimize My Airbnb Listing"<br />
            Your 100% Free Lifetime BnByond Membership Awaits!
          </h3>
          <p className="text-lg text-gray-600 mb-6">Hello fellow Airbnb Hosts!</p>
          <p className="text-gray-700 leading-relaxed max-w-4xl mx-auto">
            Want to transform your unbooked rental nights into free travel experiences?
          </p>
          <p className="text-gray-700 leading-relaxed max-w-4xl mx-auto mt-4">
            BnByond is thrilled to announce that members of Airbnb Hosts - Optimize My Airbnb Listing are being provided
            with an exclusive opportunity to join our 100% FREE lifetime membership to our revolutionary vacation rental property
            owners exchange community! This membership normally costs $88 annually, but for members of your group, it's
            entirely yours, forever. You can find out the platform at beta.bnbyond.com
          </p>
          <p className="text-gray-700 leading-relaxed max-w-4xl mx-auto mt-4">
            What is BnByond? After several years of development, we are now rolling out the ultimate booking platform where
            hosts host hosts. It's preposterously simple. You host other STR owners. You get points. You use those points to travel anywhere.
            Unlike traditional booking platforms or simple home-swap services, BnByond is an exclusive community
            designed by you for vacation rental property owners.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {/* Travel for Free */}
          <div className="bg-green-100 p-6 rounded-lg">
            <h4 className="text-xl font-bold text-green-800 mb-3">TRAVEL FOR FREE!</h4>
            <p className="text-green-700 text-sm leading-relaxed">
              Your earned points act as currency, allowing you to stay
              at other member properties around the world without the
              expense of accommodation.
            </p>
            <div className="mt-4 bg-white p-3 rounded border">
              <div className="flex justify-between items-center mb-2">
                <span className="text-xs text-gray-600">Your Lake House</span>
                <span className="text-xs text-gray-600">Escape from Stress</span>
              </div>
              <div className="text-xs text-gray-800 font-medium">
                2329 Scenic Route Road, ...
              </div>
              <div className="text-xs text-gray-600 mt-1">
                777 Bentley Dive, Dove...
              </div>
              <div className="flex justify-between mt-2">
                <span className="text-xs text-gray-500">400 Points</span>
                <span className="text-xs text-gray-500">400 Points</span>
              </div>
            </div>
          </div>

          {/* Lifetime Charter Membership */}
          <div className="bg-red-100 p-6 rounded-lg">
            <h4 className="text-xl font-bold text-red-800 mb-3">LIFETIME CHARTER MEMBERSHIP</h4>
            <p className="text-red-700 text-sm leading-relaxed">
              We are hoping that you - as a Charter Member -
              will help steer the platform and help decide on new
              features for years to come. This isn't mandatory.
            </p>
            <div className="mt-4 space-y-2">
              <div className="bg-white p-3 rounded border">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-800 font-medium">Charm on Chesser 388-3</span>
                  <span className="text-xs text-gray-500">400 Points</span>
                </div>
                <div className="text-xs text-gray-600">
                  904 Chesser Drive, Dove...
                </div>
              </div>
              <div className="bg-white p-3 rounded border">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-800 font-medium">Blue Heron Bungalow on</span>
                  <span className="text-xs text-gray-500">400 Points</span>
                </div>
                <div className="text-xs text-gray-600">
                  14 Pelican Point Drive...
                </div>
              </div>
            </div>
          </div>

          {/* Join a Trusted Community */}
          <div className="bg-yellow-100 p-6 rounded-lg">
            <h4 className="text-xl font-bold text-yellow-800 mb-3">JOIN A TRUSTED COMMUNITY</h4>
            <p className="text-yellow-700 text-sm leading-relaxed">
              Host fellow STR owners who understand the effort and
              care required for STRs, leading to more respectful guests
              and a more positive experience, tips and connections.
            </p>
          </div>

          {/* Convert Unbooked Time */}
          <div className="bg-blue-100 p-6 rounded-lg lg:col-span-3">
            <h4 className="text-xl font-bold text-blue-800 mb-3">CONVERT UNBOOKED TIME INTO VALUE</h4>
            <p className="text-blue-700 text-sm leading-relaxed">
              BnByond empowers you to leverage this downtime,
              especially during shoulder and off-peak seasons, by
              hosting other members and earning points.
            </p>
            <div className="mt-4 grid grid-cols-2 gap-4">
              <div className="bg-white p-3 rounded border">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-800 font-medium">Charm on Chesser 388-3</span>
                  <span className="text-xs text-gray-500">400 Points</span>
                </div>
                <div className="text-xs text-gray-600">
                  904 Chesser Drive, Dove...
                </div>
              </div>
              <div className="bg-white p-3 rounded border">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-800 font-medium">Blue Heron Bungalow on</span>
                  <span className="text-xs text-gray-500">400 Points</span>
                </div>
                <div className="text-xs text-gray-600">
                  14 Pelican Point Drive...
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Exclusive Offer Details */}
        <div className="bg-gray-50 p-8 rounded-lg mb-16">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            Your Exclusive Offer Details: As a Charter Member from the<br />
            "Airbnb Hosts - Optimize My Airbnb Listing" group, you receive:
          </h3>
          <ul className="space-y-3 max-w-4xl mx-auto">
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">
                <strong>100% Free Lifetime Membership</strong> (normally $88/year)!
              </span>
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">
                <strong>2,000 Starting Points:</strong> You'll receive 1,000 standard credit points PLUS an additional 2,000 bonus points to use
                for bookings right away. This gives you an incredible head start on your travel adventures!
              </span>
            </li>
          </ul>
        </div>

        {/* Simple & Transparent Fees */}
        <div className="text-center mb-16">
          <h3 className="text-3xl font-bold text-gray-800 mb-8">Simple & Transparent Fees</h3>
          <ul className="space-y-4 max-w-3xl mx-auto text-left">
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">You will pay no commission fees as a Host.</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">When you act as a Guest, there's only a modest 5% fee on the point value of your stay.</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">You will pay nothing for membership.</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-500 mr-3 mt-1">•</span>
              <span className="text-gray-700">
                List Multiple Properties: As a member of this exclusive Facebook group, you can list
                up to five properties on BnByond under your single, free lifetime membership.
              </span>
            </li>
          </ul>
        </div>

        {/* What Happens Next */}
        <div className="text-center mb-16">
          <h3 className="text-3xl font-bold text-gray-800 mb-6">What Happens Next?</h3>
          <p className="text-gray-700 leading-relaxed max-w-4xl mx-auto">
            Fill in the form below. We'll send clear instructions on how to easily fill out your profile, upload your property(s)
            details, and list out your availability and pricing on your BnByond calendar – a process designed to feel familiar, similar
            to other platforms you already use. You'll then be among the first to know when you can start booking. This is an
            incredible opportunity to maximize your STR investment and see the world thanks to your Facebook community
            Airbnb Hosts - Optimize My Airbnb Listing.
          </p>
        </div>

        {/* Join Form Section */}
        <div className="bg-gray-50 p-8 rounded-lg">
          <div className="flex flex-col lg:flex-row items-center justify-between">
            <div className="lg:w-1/2 mb-8 lg:mb-0">
              <h3 className="text-3xl font-bold text-gray-800 mb-4">
                Join Us!!! And Make the World Your Happy Place™
              </h3>

              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="First Name"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="text"
                    placeholder="Last Name"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <input
                  type="email"
                  placeholder="email"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />

                <div className="mt-6">
                  <p className="text-sm text-gray-700 mb-4">State or States where your STR is located</p>
                  <div className="space-y-2">
                    {[1, 2, 3, 4, 5].map((num) => (
                      <div key={num} className="flex items-center">
                        <span className="text-gray-600 mr-3 w-4">{num}.</span>
                        <input
                          type="text"
                          placeholder="STATE"
                          className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mt-6">
                  <button
                    type="submit"
                    className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
                  >
                    Contact Us
                  </button>
                </div>
              </form>

              <p className="text-xs text-gray-500 mt-4 leading-relaxed">
                By pressing the submit button, you are agreeing to a variety of destinations, we are
                limiting the number of properties per state. This helps to distribute
                availability to all members, while also ensuring that U.S. state
                regulations are followed. We will contact you to confirm your state.
              </p>

              <p className="text-xs text-gray-500 mt-4 leading-relaxed">
                BnByond is a Responsible Travel and vacation property network and is not affiliated with Airbnb. While we consider all host member applications, there are some
                restrictions and requirements that must be met. We reserve the right to decline applications. Please note that this is a limited time offer and subject to change.
              </p>
            </div>

            <div className="lg:w-1/2 lg:pl-8 flex justify-center">
              <div className="text-center">
                <div className="mb-4">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-orange-400 via-red-400 to-pink-400 rounded-full"></div>
                    <div className="w-6 h-6 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full"></div>
                    <div className="w-4 h-4 bg-gradient-to-br from-green-400 to-emerald-400 rounded-full"></div>
                  </div>
                  <h1 className="text-4xl font-bold text-teal-600">bnbyond</h1>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Home